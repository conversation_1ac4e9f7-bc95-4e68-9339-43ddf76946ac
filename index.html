<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Management System</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/layout.css">
    <link rel="stylesheet" href="styles/components.css">
    <script>
        // Redirect to dashboard page for clean URL routing
        window.location.replace('pages/dashboard.html');
    </script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>

            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>

            <div class="header-right">
                <button class="icon-button">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="index.html" class="nav-item active" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="pages/purchase-order.html" class="nav-item" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="pages/purchase-invoice.html" class="nav-item" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="pages/purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="pages/gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="pages/po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="pages/stock-transfer-request.html" class="nav-item" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="pages/stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Dashboard content will be loaded here -->
            <div class="page-header">
                <h2>Dashboard</h2>
                <p>Welcome to your inventory management dashboard</p>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Total Orders</h3>
                        <span class="material-icons">shopping_cart</span>
                    </div>
                    <div class="card-content">
                        <div class="metric-value">1,234</div>
                        <div class="metric-change positive">+12.5%</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Pending Invoices</h3>
                        <span class="material-icons">receipt</span>
                    </div>
                    <div class="card-content">
                        <div class="metric-value">89</div>
                        <div class="metric-change negative">-5.2%</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Stock Items</h3>
                        <span class="material-icons">inventory_2</span>
                    </div>
                    <div class="card-content">
                        <div class="metric-value">5,678</div>
                        <div class="metric-change positive">+8.1%</div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Low Stock Alerts</h3>
                        <span class="material-icons">warning</span>
                    </div>
                    <div class="card-content">
                        <div class="metric-value">23</div>
                        <div class="metric-change neutral">No change</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/navigation.js"></script>
    <script src="scripts/components.js"></script>
</body>
</html>
