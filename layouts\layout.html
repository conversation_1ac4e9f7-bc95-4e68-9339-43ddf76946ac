<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PAGE_TITLE}} - Inventory Management System</title>
    <meta name="description" content="Professional inventory management system for complete business solutions">
    
    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">
    
    <!-- Page-specific styles -->
    {{PAGE_STYLES}}
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>
            
            <div class="header-right">
                <button class="icon-button" title="Notifications">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button" title="Language">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button" title="Theme">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button" title="Settings">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="../index.html" class="nav-item {{DASHBOARD_ACTIVE}}" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item {{PURCHASE_ORDER_ACTIVE}}" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item {{PURCHASE_INVOICE_ACTIVE}}" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item {{PURCHASE_GRN_ACTIVE}}" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item {{GDR_SETTLEMENT_ACTIVE}}" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item {{PO_CANCELLATION_ACTIVE}}" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item {{STOCK_TRANSFER_ACTIVE}}" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item {{STOCK_RECEIPT_ACTIVE}}" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            {{PAGE_CONTENT}}
        </main>
    </div>

    <!-- Scripts -->
    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>
    
    <!-- Page-specific scripts -->
    {{PAGE_SCRIPTS}}
    
    <!-- Initialize page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set current page for navigation
            const currentPage = '{{CURRENT_PAGE}}';
            if (window.Navigation) {
                window.Navigation.currentPage = currentPage;
            }
            
            // Page-specific initialization
            {{PAGE_INIT}}
        });
    </script>
</body>
</html>
