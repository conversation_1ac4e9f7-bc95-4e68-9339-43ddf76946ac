<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Inventory Management System</title>
    <meta name="description" content="Inventory management dashboard with real-time metrics and insights">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">

    <style>
        /* Modern Dashboard Layout - Space Efficient Design */
        .dashboard-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: var(--spacing-lg);
            min-height: calc(100vh - 200px);
        }

        .main-dashboard {
            display: grid;
            grid-template-rows: auto 1fr auto;
            gap: var(--spacing-md);
        }

        .sidebar-dashboard {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        /* Compact Header */
        .dashboard-header {
            background: white;
            color: #000000;
            padding: var(--spacing-lg);
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .dashboard-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
        }

        .dashboard-subtitle {
            font-size: var(--font-size-sm);
            opacity: 0.8;
            margin: 0;
        }

        /* Compact Metrics Grid */
        .metrics-compact {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }

        .metric-compact {
            background: var(--color-white);
            border-radius: 12px;
            padding: var(--spacing-md);
            border: 1px solid var(--color-border);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .metric-compact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color, #000);
            transform: scaleY(0);
            transform-origin: bottom;
            transition: transform 0.3s ease;
        }

        .metric-compact:hover::before {
            transform: scaleY(1);
        }

        .metric-compact:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .metric-compact.orders { --accent-color: #2196F3; }
        .metric-compact.invoices { --accent-color: #FF9800; }
        .metric-compact.stock { --accent-color: #4CAF50; }
        .metric-compact.alerts { --accent-color: #F44336; }

        .metric-header-compact {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .metric-icon-compact {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: var(--accent-color, #000);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .metric-trend-compact {
            font-size: var(--font-size-xs);
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            background: var(--trend-bg, #f5f5f5);
            color: var(--trend-color, #666);
        }

        .metric-trend-compact.positive {
            --trend-bg: rgba(76, 175, 80, 0.1);
            --trend-color: #4CAF50;
        }

        .metric-trend-compact.negative {
            --trend-bg: rgba(244, 67, 54, 0.1);
            --trend-color: #F44336;
        }

        .metric-value-compact {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: 2px;
        }

        .metric-label-compact {
            font-size: var(--font-size-xs);
            color: var(--color-tertiary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Activity Feed - Compact */
        .activity-feed {
            background: var(--color-white);
            border-radius: 12px;
            border: 1px solid var(--color-border);
            overflow: hidden;
        }

        .activity-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--color-border);
            background: var(--color-light);
            font-weight: 600;
            font-size: var(--font-size-sm);
            color: var(--color-primary);
        }

        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            padding: var(--spacing-sm) var(--spacing-md);
            border-bottom: 1px solid var(--color-border);
            transition: background-color 0.2s ease;
        }

        .activity-item:hover {
            background-color: var(--color-hover);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
        }

        .activity-time {
            font-size: var(--font-size-xs);
            color: var(--color-quaternary);
            font-weight: 500;
        }

        .activity-type {
            font-size: var(--font-size-xs);
            padding: 1px 6px;
            border-radius: 3px;
            background: var(--color-light);
            color: var(--color-tertiary);
        }

        .activity-text {
            font-size: var(--font-size-sm);
            color: var(--color-secondary);
            line-height: 1.3;
        }

        /* Sidebar Components */
        .sidebar-card {
            background: var(--color-white);
            border-radius: 12px;
            border: 1px solid var(--color-border);
            overflow: hidden;
        }

        .sidebar-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--color-border);
            background: var(--color-light);
            font-weight: 600;
            font-size: var(--font-size-sm);
            color: var(--color-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        /* Alerts Compact */
        .alerts-list {
            padding: var(--spacing-sm);
        }

        .alert-item {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: 8px;
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }

        .alert-item:last-child {
            margin-bottom: 0;
        }

        .alert-item.warning {
            background-color: rgba(255, 152, 0, 0.1);
            border-left: 3px solid #FF9800;
        }

        .alert-item.info {
            background-color: rgba(33, 150, 243, 0.1);
            border-left: 3px solid #2196F3;
        }

        .alert-item.success {
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4CAF50;
        }

        .alert-icon {
            font-size: 1rem;
            margin-top: 2px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 2px;
            color: var(--color-primary);
        }

        .alert-desc {
            color: var(--color-tertiary);
            font-size: var(--font-size-xs);
        }

        /* Quick Stats */
        .quick-stats {
            padding: var(--spacing-md);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 2px;
        }

        .stat-label {
            font-size: var(--font-size-xs);
            color: var(--color-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Quick Actions Compact */
        .quick-actions-compact {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
        }

        .action-btn-compact {
            background: var(--color-white);
            border: 1px solid var(--color-border);
            border-radius: 8px;
            padding: var(--spacing-sm);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: var(--color-primary);
        }

        .action-btn-compact:hover {
            background: var(--color-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .action-icon {
            font-size: 1.25rem;
            margin-bottom: var(--spacing-xs);
            color: var(--color-primary);
        }

        .action-label {
            font-size: var(--font-size-xs);
            font-weight: 500;
            color: var(--color-secondary);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .sidebar-dashboard {
                order: -1;
            }

            .metrics-compact {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .metrics-compact {
                grid-template-columns: repeat(2, 1fr);
            }

            .dashboard-container {
                gap: var(--spacing-sm);
            }

            .quick-stats {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }
        }

        /* Animations */
        .metric-compact {
            animation: slideInUp 0.4s ease-out;
        }

        .metric-compact:nth-child(1) { animation-delay: 0.1s; }
        .metric-compact:nth-child(2) { animation-delay: 0.2s; }
        .metric-compact:nth-child(3) { animation-delay: 0.3s; }
        .metric-compact:nth-child(4) { animation-delay: 0.4s; }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>

            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>

            <div class="header-right">
                <button class="icon-button" title="Notifications">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button" title="Language">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button" title="Theme">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button" title="Settings">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="dashboard.html" class="nav-item active" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Space-Efficient Dashboard Layout -->
            <div class="dashboard-container">
                <!-- Main Dashboard Area -->
                <div class="main-dashboard">
                    <!-- Compact Header -->
                    <div class="dashboard-header">
                        <div class="header-content">
                            <h1 class="dashboard-title">Dashboard</h1>
                            <p class="dashboard-subtitle">Real-time insights and intelligent analytics</p>
                        </div>
                    </div>

                    <!-- Compact Metrics Grid -->
                    <div class="metrics-compact">
                        <div class="metric-compact orders">
                            <div class="metric-header-compact">
                                <div class="metric-icon-compact">
                                    <span class="material-icons">shopping_cart</span>
                                </div>
                                <div class="metric-trend-compact positive">+12.5%</div>
                            </div>
                            <div class="metric-value-compact" data-target="1234">0</div>
                            <div class="metric-label-compact">Total Orders</div>
                        </div>

                        <div class="metric-compact invoices">
                            <div class="metric-header-compact">
                                <div class="metric-icon-compact">
                                    <span class="material-icons">receipt</span>
                                </div>
                                <div class="metric-trend-compact negative">-5.2%</div>
                            </div>
                            <div class="metric-value-compact" data-target="89">0</div>
                            <div class="metric-label-compact">Pending Invoices</div>
                        </div>

                        <div class="metric-compact stock">
                            <div class="metric-header-compact">
                                <div class="metric-icon-compact">
                                    <span class="material-icons">inventory_2</span>
                                </div>
                                <div class="metric-trend-compact positive">+8.1%</div>
                            </div>
                            <div class="metric-value-compact" data-target="5678">0</div>
                            <div class="metric-label-compact">Stock Items</div>
                        </div>

                        <div class="metric-compact alerts">
                            <div class="metric-header-compact">
                                <div class="metric-icon-compact">
                                    <span class="material-icons">warning</span>
                                </div>
                                <div class="metric-trend-compact">No change</div>
                            </div>
                            <div class="metric-value-compact" data-target="23">0</div>
                            <div class="metric-label-compact">Low Stock Alerts</div>
                        </div>
                    </div>

                    <!-- Activity Feed -->
                    <div class="activity-feed">
                        <div class="activity-header">
                            <span class="material-icons">trending_up</span>
                            Recent Activity
                        </div>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">2 min ago</span>
                                    <span class="activity-type">ORDER</span>
                                </div>
                                <div class="activity-text">New purchase order #PO-2024-001 created</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">15 min ago</span>
                                    <span class="activity-type">TRANSFER</span>
                                </div>
                                <div class="activity-text">Stock transfer completed for Item #ST-456</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">1 hour ago</span>
                                    <span class="activity-type">INVOICE</span>
                                </div>
                                <div class="activity-text">Invoice #INV-2024-089 approved</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">2 hours ago</span>
                                    <span class="activity-type">GRN</span>
                                </div>
                                <div class="activity-text">GRN #GRN-2024-045 processed</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">3 hours ago</span>
                                    <span class="activity-type">PAYMENT</span>
                                </div>
                                <div class="activity-text">Payment processed for supplier ABC Ltd.</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-meta">
                                    <span class="activity-time">4 hours ago</span>
                                    <span class="activity-type">STOCK</span>
                                </div>
                                <div class="activity-text">Stock level updated for 15 items</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Dashboard -->
                <div class="sidebar-dashboard">
                    <!-- Alerts & Notifications -->
                    <div class="sidebar-card">
                        <div class="sidebar-header">
                            <span class="material-icons">notifications</span>
                            Alerts
                        </div>
                        <div class="alerts-list">
                            <div class="alert-item warning">
                                <span class="material-icons alert-icon">warning</span>
                                <div class="alert-content">
                                    <div class="alert-title">Low Stock Alert</div>
                                    <div class="alert-desc">23 items running low</div>
                                </div>
                            </div>
                            <div class="alert-item info">
                                <span class="material-icons alert-icon">info</span>
                                <div class="alert-content">
                                    <div class="alert-title">Pending Approvals</div>
                                    <div class="alert-desc">5 orders need review</div>
                                </div>
                            </div>
                            <div class="alert-item success">
                                <span class="material-icons alert-icon">check_circle</span>
                                <div class="alert-content">
                                    <div class="alert-title">Report Generated</div>
                                    <div class="alert-desc">Monthly inventory report</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="sidebar-card">
                        <div class="sidebar-header">
                            <span class="material-icons">analytics</span>
                            Quick Stats
                        </div>
                        <div class="quick-stats">
                            <div class="stat-item">
                                <div class="stat-value">$2.4M</div>
                                <div class="stat-label">Monthly Revenue</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">94.2%</div>
                                <div class="stat-label">Order Accuracy</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">1.8 days</div>
                                <div class="stat-label">Avg Processing</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">156</div>
                                <div class="stat-label">Active Suppliers</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="sidebar-card">
                        <div class="sidebar-header">
                            <span class="material-icons">flash_on</span>
                            Quick Actions
                        </div>
                        <div class="quick-actions-compact">
                            <a href="purchase-order.html" class="action-btn-compact">
                                <div class="action-icon">
                                    <span class="material-icons">add_shopping_cart</span>
                                </div>
                                <div class="action-label">New Order</div>
                            </a>
                            <a href="purchase-invoice.html" class="action-btn-compact">
                                <div class="action-icon">
                                    <span class="material-icons">receipt_long</span>
                                </div>
                                <div class="action-label">New Invoice</div>
                            </a>
                            <a href="stock-transfer-request.html" class="action-btn-compact">
                                <div class="action-icon">
                                    <span class="material-icons">swap_horiz</span>
                                </div>
                                <div class="action-label">Transfer</div>
                            </a>
                            <a href="stock-receipt-grn.html" class="action-btn-compact">
                                <div class="action-icon">
                                    <span class="material-icons">inventory</span>
                                </div>
                                <div class="action-label">Receipt</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize unified design system
            if (window.App) {
                window.App.init();
            }

            // Set current page for navigation
            if (window.Navigation) {
                window.Navigation.currentPage = 'dashboard';
            }

            // Initialize compact dashboard features
            initializeCompactDashboard();
        });

        function initializeCompactDashboard() {
            // Animate metric values
            setTimeout(() => {
                animateMetricValues();
            }, 500);

            // Add click handlers for metric cards
            document.querySelectorAll('.metric-compact').forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.classList[1]; // Get the metric type (orders, invoices, etc.)
                    showMetricDetails(type);
                });
            });

            // Add hover effects for activity items
            document.querySelectorAll('.activity-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(4px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });

            // Add click handlers for alert items
            document.querySelectorAll('.alert-item').forEach(alert => {
                alert.addEventListener('click', function() {
                    const title = this.querySelector('.alert-title').textContent;
                    const desc = this.querySelector('.alert-desc').textContent;
                    showAlertDetails(title, desc);
                });

                alert.style.cursor = 'pointer';
            });

            // Auto-refresh activity feed every 30 seconds
            setInterval(refreshActivityFeed, 30000);
        }

        function animateMetricValues() {
            document.querySelectorAll('.metric-value-compact').forEach(element => {
                const target = parseInt(element.getAttribute('data-target'));
                animateValue(element, target);
            });
        }

        function animateValue(element, target) {
            let current = 0;
            const increment = target / 50; // 50 frames for smooth animation
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20); // ~50fps
        }

        function showMetricDetails(type) {
            const details = {
                orders: {
                    title: 'Total Orders Details',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #f0f8ff; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #2196F3;">1,234</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f0f8ff; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">+12.5%</div>
                                <div style="font-size: 0.875rem; color: #666;">Growth Rate</div>
                            </div>
                        </div>
                        <p><strong>Performance Summary:</strong></p>
                        <ul style="margin: 1rem 0;">
                            <li>New orders this month: 456</li>
                            <li>Recurring orders: 778</li>
                            <li>Average order value: $1,945</li>
                            <li>Top performing category: Electronics</li>
                        </ul>
                        <p>Your order volume shows strong growth with excellent customer retention rates.</p>
                    `
                },
                invoices: {
                    title: 'Pending Invoices Details',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #fff8e1; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">89</div>
                                <div style="font-size: 0.875rem; color: #666;">Pending Invoices</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #fff8e1; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">$245K</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Value</div>
                            </div>
                        </div>
                        <p><strong>Invoice Breakdown:</strong></p>
                        <ul style="margin: 1rem 0;">
                            <li>Overdue (>30 days): 12 invoices</li>
                            <li>Due this week: 23 invoices</li>
                            <li>Due next week: 34 invoices</li>
                            <li>Future due dates: 20 invoices</li>
                        </ul>
                        <p>Focus on collecting overdue invoices to improve cash flow.</p>
                    `
                },
                stock: {
                    title: 'Stock Items Details',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">5,678</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Items</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">$3.2M</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Value</div>
                            </div>
                        </div>
                        <p><strong>Inventory Status:</strong></p>
                        <ul style="margin: 1rem 0;">
                            <li>In stock: 4,892 items (86%)</li>
                            <li>Low stock: 563 items (10%)</li>
                            <li>Out of stock: 223 items (4%)</li>
                            <li>New arrivals this week: 145 items</li>
                        </ul>
                        <p>Inventory levels are healthy with good turnover rates.</p>
                    `
                },
                alerts: {
                    title: 'Low Stock Alerts Details',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #ffebee; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #F44336;">23</div>
                                <div style="font-size: 0.875rem; color: #666;">Critical Items</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #ffebee; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #F44336;">$89K</div>
                                <div style="font-size: 0.875rem; color: #666;">Reorder Value</div>
                            </div>
                        </div>
                        <p><strong>Critical Items Requiring Attention:</strong></p>
                        <ul style="margin: 1rem 0;">
                            <li>Electronics components: 8 items</li>
                            <li>Office supplies: 6 items</li>
                            <li>Raw materials: 5 items</li>
                            <li>Safety equipment: 4 items</li>
                        </ul>
                        <p>Immediate reordering recommended to prevent stockouts.</p>
                    `
                }
            };

            if (window.Components && details[type]) {
                window.Components.createModal(
                    details[type].title,
                    details[type].content,
                    { width: '600px' }
                );
            }
        }

        function showAlertDetails(title, desc) {
            if (window.Components) {
                const content = `
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <span class="material-icons" style="font-size: 3rem; color: #FF9800;">warning</span>
                    </div>
                    <h3 style="text-align: center; margin-bottom: 1rem;">${title}</h3>
                    <p style="text-align: center; color: #666; margin-bottom: 1.5rem;">${desc}</p>
                    <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px;">
                        <p><strong>Recommended Actions:</strong></p>
                        <ul style="margin: 0.5rem 0;">
                            <li>Review inventory levels immediately</li>
                            <li>Contact suppliers for urgent reorders</li>
                            <li>Update reorder points if necessary</li>
                            <li>Monitor stock levels daily</li>
                        </ul>
                    </div>
                `;

                window.Components.createModal(
                    'Alert Details',
                    content,
                    { width: '500px' }
                );
            }
        }

        function refreshActivityFeed() {
            // Simulate new activity
            const activities = [
                { time: 'Just now', type: 'ORDER', text: 'New purchase order #PO-2024-' + Math.floor(Math.random() * 1000) + ' created' },
                { time: '1 min ago', type: 'PAYMENT', text: 'Payment received from customer #' + Math.floor(Math.random() * 1000) },
                { time: '3 min ago', type: 'STOCK', text: 'Stock level updated for ' + Math.floor(Math.random() * 50) + ' items' }
            ];

            const activityList = document.querySelector('.activity-list');
            const randomActivity = activities[Math.floor(Math.random() * activities.length)];

            // Add new activity at the top
            const newItem = document.createElement('div');
            newItem.className = 'activity-item';
            newItem.style.opacity = '0';
            newItem.style.transform = 'translateY(-10px)';
            newItem.innerHTML = `
                <div class="activity-meta">
                    <span class="activity-time">${randomActivity.time}</span>
                    <span class="activity-type">${randomActivity.type}</span>
                </div>
                <div class="activity-text">${randomActivity.text}</div>
            `;

            activityList.insertBefore(newItem, activityList.firstChild);

            // Animate in
            setTimeout(() => {
                newItem.style.opacity = '1';
                newItem.style.transform = 'translateY(0)';
                newItem.style.transition = 'all 0.3s ease';
            }, 100);

            // Remove last item if more than 6 items
            const items = activityList.querySelectorAll('.activity-item');
            if (items.length > 6) {
                items[items.length - 1].remove();
            }
        }
    </script>
</body>
</html>
