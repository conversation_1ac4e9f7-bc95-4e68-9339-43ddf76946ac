// Main JavaScript functionality for the Inventory Management System

// Global app state
const App = {
    currentPage: 'dashboard',
    user: {
        name: '<PERSON>',
        role: 'System Administrator',
        avatar: 'J<PERSON>'
    },
    isInitialized: false,

    // Initialize the application with unified design system
    init() {
        if (this.isInitialized) return;

        this.setupEventListeners();
        this.setupSearch();
        this.setupKeyboardShortcuts();
        this.initializeModernComponents();
        this.ensureDesignConsistency();

        this.isInitialized = true;
        console.log('App initialized with unified design system');
    },

    // Initialize modern components across all pages
    initializeModernComponents() {
        // Initialize metric animations if present
        this.initializeMetrics();

        // Initialize modern buttons
        this.initializeModernButtons();

        // Initialize modern tables
        this.initializeModernTables();

        // Initialize modern cards
        this.initializeModernCards();

        // Initialize status badges
        this.initializeStatusBadges();
    },

    // Ensure design consistency across all pages
    ensureDesignConsistency() {
        // Apply modern classes to existing elements
        this.upgradeToModernDesign();

        // Fix navigation active states
        this.updateNavigationState();

        // Initialize page-specific animations
        this.initializePageAnimations();
    },

    // Upgrade existing elements to modern design
    upgradeToModernDesign() {
        // Upgrade buttons
        document.querySelectorAll('.btn:not(.btn-modern)').forEach(btn => {
            btn.classList.add('btn-modern');
            if (btn.classList.contains('btn-primary')) {
                btn.classList.add('primary');
            }
            if (btn.classList.contains('btn-secondary')) {
                btn.classList.add('secondary');
            }
        });

        // Upgrade tables
        document.querySelectorAll('.table-container:not(.table-modern)').forEach(container => {
            container.classList.add('table-modern');
        });

        // Upgrade cards
        document.querySelectorAll('.card:not(.card-modern)').forEach(card => {
            card.classList.add('card-modern');
        });

        // Upgrade status badges
        document.querySelectorAll('.status-badge').forEach(badge => {
            badge.classList.add('status-modern');
        });

        // Upgrade action buttons
        document.querySelectorAll('.action-btn:not(.action-btn-modern)').forEach(btn => {
            btn.classList.add('action-btn-modern');
        });
    },

    // Initialize metrics with animations
    initializeMetrics() {
        const metricValues = document.querySelectorAll('[data-target]');
        if (metricValues.length > 0) {
            setTimeout(() => {
                metricValues.forEach(element => {
                    const target = parseInt(element.getAttribute('data-target'));
                    const format = element.getAttribute('data-format');
                    this.animateValue(element, target, format);
                });
            }, 500);
        }
    },

    // Initialize modern buttons
    initializeModernButtons() {
        document.querySelectorAll('.btn-modern').forEach(btn => {
            if (!btn.hasAttribute('data-modern-initialized')) {
                btn.addEventListener('click', function(e) {
                    // Add click ripple effect
                    const ripple = document.createElement('span');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(255,255,255,0.6);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });

                btn.setAttribute('data-modern-initialized', 'true');
            }
        });

        // Add ripple animation CSS if not present
        if (!document.querySelector('#ripple-animation')) {
            const style = document.createElement('style');
            style.id = 'ripple-animation';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    },

    // Initialize modern tables
    initializeModernTables() {
        document.querySelectorAll('.table-modern table').forEach(table => {
            // Add sorting functionality
            table.querySelectorAll('th').forEach(header => {
                if (!header.hasAttribute('data-sort-initialized')) {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => this.sortTable(header));
                    header.setAttribute('data-sort-initialized', 'true');
                }
            });
        });
    },

    // Initialize modern cards
    initializeModernCards() {
        document.querySelectorAll('.card-modern, .metric-modern').forEach(card => {
            if (!card.hasAttribute('data-card-initialized')) {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });

                card.setAttribute('data-card-initialized', 'true');
            }
        });
    },

    // Initialize status badges
    initializeStatusBadges() {
        document.querySelectorAll('.status-modern').forEach(badge => {
            // Add pulse animation for warning/error states
            if (badge.classList.contains('warning') || badge.classList.contains('error')) {
                badge.style.animation = 'pulse 2s infinite';
            }
        });

        // Add pulse animation CSS if not present
        if (!document.querySelector('#pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'pulse-animation';
            style.textContent = `
                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.7; }
                }
            `;
            document.head.appendChild(style);
        }
    },

    // Update navigation active state (delegated to Navigation module)
    updateNavigationState() {
        // Navigation state is now handled by the Navigation module
        if (window.Navigation && window.Navigation.updateActiveNavItem) {
            window.Navigation.updateActiveNavItem();
        }
    },

    // Get current page from path (delegated to Navigation module)
    getCurrentPageFromPath(path) {
        if (window.Navigation && window.Navigation.getCurrentPageFromPath) {
            return window.Navigation.getCurrentPageFromPath(path);
        }
        return 'dashboard';
    },

    // Initialize page animations
    initializePageAnimations() {
        // Add staggered animations to cards and metrics
        document.querySelectorAll('.metric-modern, .card-modern').forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
            element.classList.add('animate-fade-scale');
        });

        // Add slide-up animation to tables
        document.querySelectorAll('.table-modern').forEach(table => {
            table.classList.add('animate-slide-up');
        });
    },

    // Set up global event listeners
    setupEventListeners() {
        // Header icon buttons
        document.querySelectorAll('.icon-button').forEach(button => {
            button.addEventListener('click', this.handleIconButtonClick.bind(this));
        });

        // User profile dropdown
        const userProfile = document.querySelector('.user-profile');
        if (userProfile) {
            userProfile.addEventListener('click', this.toggleUserDropdown.bind(this));
        }

        // Window resize handler
        window.addEventListener('resize', this.handleResize.bind(this));

        // Close dropdowns when clicking outside
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    },

    // Set up search functionality
    setupSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
            searchInput.addEventListener('keydown', this.handleSearchKeydown.bind(this));
        }
    },

    // Set up keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+K for search focus
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape to close modals/dropdowns
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
                this.closeAllModals();
            }
        });
    },

    // Handle icon button clicks
    handleIconButtonClick(e) {
        const button = e.currentTarget;
        const icon = button.querySelector('.material-icons').textContent;

        switch (icon) {
            case 'notifications':
                this.showNotifications();
                break;
            case 'language':
                this.showLanguageSelector();
                break;
            case 'light_mode':
                this.toggleTheme();
                break;
            case 'settings':
                this.showSettings();
                break;
        }
    },

    // Handle search input
    handleSearch(e) {
        const query = e.target.value.trim();
        if (query.length > 2) {
            this.performSearch(query);
        } else {
            this.clearSearchResults();
        }
    },

    // Handle search keydown events
    handleSearchKeydown(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = e.target.value.trim();
            if (query) {
                this.performSearch(query);
            }
        }
    },

    // Perform search operation
    performSearch(query) {
        console.log('Searching for:', query);
        // TODO: Implement actual search functionality
        this.showSearchResults(query);
    },

    // Show search results
    showSearchResults(query) {
        // TODO: Display search results in a dropdown or modal
        console.log('Showing search results for:', query);
    },

    // Clear search results
    clearSearchResults() {
        // TODO: Clear search results display
        console.log('Clearing search results');
    },

    // Toggle user dropdown
    toggleUserDropdown() {
        // TODO: Implement user dropdown menu
        console.log('Toggle user dropdown');
    },

    // Show notifications
    showNotifications() {
        this.showModal('Notifications', this.getNotificationsContent());
    },

    // Show language selector
    showLanguageSelector() {
        this.showModal('Language Settings', this.getLanguageSelectorContent());
    },

    // Toggle theme (light/dark mode placeholder)
    toggleTheme() {
        console.log('Theme toggle - feature coming soon');
        this.showToast('Theme switching coming soon!');
    },

    // Show settings
    showSettings() {
        this.showModal('Settings', this.getSettingsContent());
    },

    // Load dashboard data
    loadDashboardData() {
        // Simulate loading dashboard metrics
        const metrics = [
            { value: '1,234', change: '+12.5%', type: 'positive' },
            { value: '89', change: '-5.2%', type: 'negative' },
            { value: '5,678', change: '+8.1%', type: 'positive' },
            { value: '23', change: 'No change', type: 'neutral' }
        ];

        this.updateDashboardMetrics(metrics);
    },

    // Update dashboard metrics
    updateDashboardMetrics(metrics) {
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            if (metrics[index]) {
                const valueElement = card.querySelector('.metric-value');
                const changeElement = card.querySelector('.metric-change');

                if (valueElement) {
                    this.animateValue(valueElement, metrics[index].value);
                }

                if (changeElement) {
                    changeElement.textContent = metrics[index].change;
                    changeElement.className = `metric-change ${metrics[index].type}`;
                }
            }
        });
    },

    // Animate number values with optional formatting
    animateValue(element, targetValue, format = null) {
        let numericValue;

        if (typeof targetValue === 'number') {
            numericValue = targetValue;
        } else {
            numericValue = parseInt(targetValue.toString().replace(/[^0-9.-]/g, ''));
        }

        if (isNaN(numericValue)) {
            element.textContent = targetValue;
            return;
        }

        let currentValue = 0;
        const increment = numericValue / 50;
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= numericValue) {
                currentValue = numericValue;
                clearInterval(timer);
            }

            if (format === 'currency') {
                if (numericValue >= 1000000) {
                    element.textContent = '$' + (currentValue / 1000000).toFixed(1) + 'M';
                } else if (numericValue >= 1000) {
                    element.textContent = '$' + (currentValue / 1000).toFixed(1) + 'K';
                } else {
                    element.textContent = '$' + Math.floor(currentValue).toLocaleString();
                }
            } else {
                element.textContent = Math.floor(currentValue).toLocaleString();
            }
        }, 20);
    },

    // Sort table functionality
    sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');

        // Remove existing sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // Add new sort class
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');

        // Sort rows
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();

            // Try to parse as numbers
            const aNum = parseFloat(aText.replace(/[^0-9.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^0-9.-]/g, ''));

            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? bNum - aNum : aNum - bNum;
            } else {
                return isAscending ? bText.localeCompare(aText) : aText.localeCompare(bText);
            }
        });

        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    },

    // Handle window resize
    handleResize() {
        // Adjust layout for mobile/desktop
        this.adjustLayoutForScreenSize();
    },

    // Adjust layout based on screen size
    adjustLayoutForScreenSize() {
        const isMobile = window.innerWidth <= 768;
        document.body.classList.toggle('mobile-layout', isMobile);
    },

    // Handle clicks outside dropdowns/modals
    handleOutsideClick(e) {
        // Close dropdowns if clicking outside
        if (!e.target.closest('.user-profile')) {
            this.closeAllDropdowns();
        }
    },

    // Close all dropdowns
    closeAllDropdowns() {
        document.querySelectorAll('.dropdown.active').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
    },

    // Close all modals
    closeAllModals() {
        document.querySelectorAll('.modal-overlay.active').forEach(modal => {
            modal.classList.remove('active');
        });
    },

    // Show modal
    showModal(title, content) {
        const modalHTML = `
            <div class="modal-overlay active">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">${title}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add close event listener
        const modal = document.querySelector('.modal-overlay:last-child');
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => modal.remove());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    },

    // Show toast notification
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    },

    // Get notifications content
    getNotificationsContent() {
        return `
            <div class="notifications-list">
                <div class="notification-item">
                    <span class="material-icons">info</span>
                    <div>
                        <strong>System Update</strong>
                        <p>New features available in inventory management</p>
                        <small>2 hours ago</small>
                    </div>
                </div>
                <div class="notification-item">
                    <span class="material-icons">warning</span>
                    <div>
                        <strong>Low Stock Alert</strong>
                        <p>23 items are running low on stock</p>
                        <small>4 hours ago</small>
                    </div>
                </div>
            </div>
        `;
    },

    // Get language selector content
    getLanguageSelectorContent() {
        return `
            <div class="language-options">
                <label><input type="radio" name="language" value="en" checked> English</label>
                <label><input type="radio" name="language" value="es"> Spanish</label>
                <label><input type="radio" name="language" value="fr"> French</label>
                <label><input type="radio" name="language" value="de"> German</label>
            </div>
        `;
    },

    // Get settings content
    getSettingsContent() {
        return `
            <div class="settings-form">
                <div class="form-group">
                    <label class="form-label">Theme</label>
                    <select class="form-input">
                        <option>Light</option>
                        <option>Dark</option>
                        <option>Auto</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Notifications</label>
                    <label><input type="checkbox" checked> Email notifications</label>
                    <label><input type="checkbox" checked> Push notifications</label>
                </div>
            </div>
        `;
    }
};

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// Export for use in other modules
window.App = App;
