<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Invoices - Inventory Management System</title>
    <meta name="description" content="View and manage purchase invoices in your inventory management system">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">

    <style>
        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .actions-left {
            display: flex;
            gap: var(--spacing-md);
        }

        .actions-right {
            display: flex;
            gap: var(--spacing-sm);
        }

        .filter-container {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-select {
            min-width: 150px;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background-color: var(--color-white);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            border: 1px solid var(--color-border);
            text-align: center;
        }

        .stat-value {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--color-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .invoice-amount {
            font-weight: 600;
            color: var(--color-primary);
        }

        .overdue {
            color: var(--color-error);
        }

        @media (max-width: 768px) {
            .page-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .actions-left,
            .actions-right {
                justify-content: center;
            }

            .filter-container {
                flex-direction: column;
            }

            .filter-select {
                min-width: auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>

            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>

            <div class="header-right">
                <button class="icon-button" title="Notifications">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button" title="Language">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button" title="Theme">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button" title="Settings">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="dashboard.html" class="nav-item" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item active" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Modern Header -->
            <div class="modern-header">
                <div class="modern-header-content">
                    <h1 class="modern-title">Purchase Invoices</h1>
                    <p class="modern-subtitle">Advanced invoice management with payment tracking and analytics</p>
                </div>
            </div>

            <!-- Modern Metrics -->
            <div class="metrics-modern">
                <div class="metric-modern primary">
                    <div class="metric-header-modern">
                        <div class="metric-icon-modern">
                            <span class="material-icons">receipt</span>
                        </div>
                        <div class="metric-trend-modern positive">+8.2%</div>
                    </div>
                    <div class="metric-value-modern" data-target="456">0</div>
                    <div class="metric-label-modern">Total Invoices</div>
                </div>

                <div class="metric-modern warning">
                    <div class="metric-header-modern">
                        <div class="metric-icon-modern">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="metric-trend-modern negative">-3.1%</div>
                    </div>
                    <div class="metric-value-modern" data-target="89">0</div>
                    <div class="metric-label-modern">Pending Payment</div>
                </div>

                <div class="metric-modern error">
                    <div class="metric-header-modern">
                        <div class="metric-icon-modern">
                            <span class="material-icons">warning</span>
                        </div>
                        <div class="metric-trend-modern positive">-15.3%</div>
                    </div>
                    <div class="metric-value-modern" data-target="12">0</div>
                    <div class="metric-label-modern">Overdue</div>
                </div>

                <div class="metric-modern success">
                    <div class="metric-header-modern">
                        <div class="metric-icon-modern">
                            <span class="material-icons">attach_money</span>
                        </div>
                        <div class="metric-trend-modern positive">+12.7%</div>
                    </div>
                    <div class="metric-value-modern" data-target="1800000" data-format="currency">$0</div>
                    <div class="metric-label-modern">Total Amount</div>
                </div>
            </div>

            <!-- Modern Action Bar -->
            <div class="action-bar-modern">
                <div class="actions-left-modern">
                    <button class="btn-modern primary" id="new-invoice-btn">
                        <span class="material-icons">add</span>
                        New Invoice
                    </button>
                    <button class="btn-modern secondary" id="bulk-payment-btn">
                        <span class="material-icons">payment</span>
                        Bulk Payment
                    </button>
                </div>

                <div class="actions-right-modern">
                    <select class="filter-modern">
                        <option value="">All Status</option>
                        <option value="pending">Pending Payment</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <select class="filter-modern">
                        <option value="">All Suppliers</option>
                        <option value="abc">ABC Suppliers Ltd.</option>
                        <option value="xyz">XYZ Trading Co.</option>
                        <option value="def">DEF Industries</option>
                    </select>
                    <button class="btn-modern secondary">
                        <span class="material-icons">file_download</span>
                        Export
                    </button>
                    <button class="btn-modern secondary" id="refresh-btn">
                        <span class="material-icons">refresh</span>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Modern Table -->
            <div class="table-modern">
                <table>
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>Invoice Number</th>
                            <th>Supplier</th>
                            <th>PO Reference</th>
                            <th>Invoice Date</th>
                            <th>Due Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="row-select"></td>
                            <td><strong>INV-2024-001</strong></td>
                            <td>ABC Suppliers Ltd.</td>
                            <td>PO-2024-001</td>
                            <td>2024-01-15</td>
                            <td>2024-02-14</td>
                            <td><span class="invoice-amount">$12,500.00</span></td>
                            <td><span class="status-modern success">Paid</span></td>
                            <td>
                                <div class="action-buttons-modern">
                                    <button class="action-btn-modern" title="View Details">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="action-btn-modern" title="Edit">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn-modern" title="Download PDF">
                                        <span class="material-icons">file_download</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-select"></td>
                            <td><strong>INV-2024-002</strong></td>
                            <td>XYZ Trading Co.</td>
                            <td>PO-2024-002</td>
                            <td>2024-01-14</td>
                            <td>2024-02-13</td>
                            <td><span class="invoice-amount">$8,750.00</span></td>
                            <td><span class="status-modern warning">Pending Payment</span></td>
                            <td>
                                <div class="action-buttons-modern">
                                    <button class="action-btn-modern" title="View Details">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="action-btn-modern" title="Edit">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn-modern" title="Mark as Paid" style="color: var(--color-success);">
                                        <span class="material-icons">payment</span>
                                    </button>
                                    <button class="action-btn-modern" title="Download PDF">
                                        <span class="material-icons">file_download</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-select"></td>
                            <td><strong>INV-2024-003</strong></td>
                            <td>DEF Industries</td>
                            <td>PO-2024-003</td>
                            <td>2024-01-10</td>
                            <td class="overdue">2024-01-25</td>
                            <td><span class="invoice-amount overdue">$15,200.00</span></td>
                            <td><span class="status-modern error">Overdue</span></td>
                            <td>
                                <div class="action-buttons-modern">
                                    <button class="action-btn-modern" title="View Details">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="action-btn-modern" title="Edit">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn-modern" title="Mark as Paid" style="color: var(--color-success);">
                                        <span class="material-icons">payment</span>
                                    </button>
                                    <button class="action-btn-modern" title="Send Reminder" style="color: var(--color-warning);">
                                        <span class="material-icons">email</span>
                                    </button>
                                    <button class="action-btn-modern" title="Download PDF">
                                        <span class="material-icons">file_download</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="row-select"></td>
                            <td><strong>INV-2024-004</strong></td>
                            <td>ABC Suppliers Ltd.</td>
                            <td>PO-2024-004</td>
                            <td>2024-01-12</td>
                            <td>2024-02-11</td>
                            <td><span class="invoice-amount">$6,800.00</span></td>
                            <td><span class="status-modern warning">Pending Payment</span></td>
                            <td>
                                <div class="action-buttons-modern">
                                    <button class="action-btn-modern" title="View Details">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="action-btn-modern" title="Edit">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn-modern" title="Mark as Paid" style="color: var(--color-success);">
                                        <span class="material-icons">payment</span>
                                    </button>
                                    <button class="action-btn-modern" title="Download PDF">
                                        <span class="material-icons">file_download</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="pagination">
                    <div class="pagination-info">
                        Showing 1-4 of 456 invoices
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">...</button>
                        <button class="pagination-btn">114</button>
                        <button class="pagination-btn">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize unified design system
            if (window.App) {
                window.App.init();
            }

            // Set current page for navigation
            if (window.Navigation) {
                window.Navigation.currentPage = 'purchase-invoice';
            }

            // Initialize purchase invoice specific features
            initializePurchaseInvoicePage();
        });

        function initializePurchaseInvoicePage() {
            // New Invoice button
            document.getElementById('new-invoice-btn').addEventListener('click', function() {
                if (window.Components) {
                    const modal = window.Components.createModal(
                        'Create New Invoice',
                        getInvoiceForm(),
                        {
                            width: '800px',
                            footer: `
                                <button class="btn btn-secondary" onclick="window.Components.closeModal(this.closest('.modal-backdrop'))">Cancel</button>
                                <button class="btn btn-primary" onclick="saveInvoice()">Create Invoice</button>
                            `
                        }
                    );
                }
            });

            // Bulk Payment button
            document.getElementById('bulk-payment-btn').addEventListener('click', function() {
                const selectedRows = document.querySelectorAll('.row-select:checked');
                if (selectedRows.length === 0) {
                    if (window.Components) {
                        window.Components.showToast('Please select invoices to process payment', 'warning');
                    }
                    return;
                }

                if (window.Components) {
                    window.Components.confirm(
                        `Process payment for ${selectedRows.length} selected invoice(s)?`,
                        () => {
                            window.Components.showToast(`Payment processed for ${selectedRows.length} invoice(s)`, 'success');
                            // Update status badges to "Paid"
                            selectedRows.forEach(checkbox => {
                                const row = checkbox.closest('tr');
                                const statusBadge = row.querySelector('.status-badge');
                                statusBadge.className = 'status-badge success';
                                statusBadge.textContent = 'Paid';
                                checkbox.checked = false;
                            });
                        }
                    );
                }
            });

            // Select all checkbox
            document.getElementById('select-all').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.row-select');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // Action buttons
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', handleTableAction);
            });

            // Filter functionality
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', applyFilters);
            });
        }

        function getInvoiceForm() {
            return `
                <form class="form-container" id="invoice-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Purchase Order *</label>
                            <select class="form-input" required>
                                <option value="">Select Purchase Order</option>
                                <option value="po-001">PO-2024-001 - ABC Suppliers Ltd.</option>
                                <option value="po-002">PO-2024-002 - XYZ Trading Co.</option>
                                <option value="po-003">PO-2024-003 - DEF Industries</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Invoice Date *</label>
                            <input type="date" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Due Date *</label>
                            <input type="date" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Payment Terms</label>
                            <select class="form-input">
                                <option value="net30">Net 30</option>
                                <option value="net15">Net 15</option>
                                <option value="net60">Net 60</option>
                                <option value="cod">Cash on Delivery</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Notes</label>
                        <textarea class="form-input" rows="3" placeholder="Additional notes or comments..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Invoice Amount *</label>
                        <input type="number" class="form-input" placeholder="0.00" step="0.01" required>
                    </div>
                </form>
            `;
        }

        function saveInvoice() {
            if (window.Components) {
                window.Components.showToast('Invoice created successfully!', 'success');
                // Close modal
                const modal = document.querySelector('.modal-backdrop');
                if (modal) {
                    window.Components.closeModal(modal);
                }
            }
        }

        function handleTableAction(e) {
            const button = e.currentTarget;
            const icon = button.querySelector('.material-icons').textContent;
            const row = button.closest('tr');
            const invoiceNumber = row.querySelector('td:nth-child(2) strong').textContent;

            switch (icon) {
                case 'visibility':
                    if (window.Components) {
                        window.Components.showToast(`Viewing details for ${invoiceNumber}`, 'info');
                    }
                    break;
                case 'edit':
                    if (window.Components) {
                        window.Components.showToast(`Editing ${invoiceNumber}`, 'info');
                    }
                    break;
                case 'payment':
                    if (window.Components) {
                        window.Components.confirm(
                            `Mark ${invoiceNumber} as paid?`,
                            () => {
                                const statusBadge = row.querySelector('.status-badge');
                                statusBadge.className = 'status-badge success';
                                statusBadge.textContent = 'Paid';
                                window.Components.showToast(`${invoiceNumber} marked as paid`, 'success');
                            }
                        );
                    }
                    break;
                case 'email':
                    if (window.Components) {
                        window.Components.showToast(`Sending payment reminder for ${invoiceNumber}`, 'info');
                    }
                    break;
                case 'file_download':
                    if (window.Components) {
                        window.Components.showToast(`Downloading PDF for ${invoiceNumber}`, 'info');
                    }
                    break;
            }
        }

        function applyFilters() {
            // TODO: Implement filtering logic
            if (window.Components) {
                window.Components.showToast('Filters applied', 'info');
            }
        }
    </script>
</body>
</html>
