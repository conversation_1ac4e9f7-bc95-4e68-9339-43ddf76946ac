// Reusable components and utilities for the Inventory Management System

const Components = {
    // Initialize all components
    init() {
        this.initializeToasts();
        this.initializeModals();
        this.initializeTables();
        this.initializeForms();
    },
    
    // Toast notification system
    initializeToasts() {
        // Create toast container if it doesn't exist
        if (!document.querySelector('.toast-container')) {
            const container = document.createElement('div');
            container.className = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1100;
                display: flex;
                flex-direction: column;
                gap: 10px;
            `;
            document.body.appendChild(container);
        }
    },
    
    // Show toast notification
    showToast(message, type = 'info', duration = 3000) {
        const container = document.querySelector('.toast-container');
        const toast = document.createElement('div');
        
        const icons = {
            success: 'check_circle',
            error: 'error',
            warning: 'warning',
            info: 'info'
        };
        
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            background: var(--color-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            border-left: 4px solid var(--color-${type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'});
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        `;
        
        toast.innerHTML = `
            <span class="material-icons" style="color: var(--color-${type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'})">${icons[type] || icons.info}</span>
            <span style="flex: 1; font-size: var(--font-size-sm);">${message}</span>
            <button class="toast-close" style="background: none; border: none; cursor: pointer; color: var(--color-tertiary);">
                <span class="material-icons">close</span>
            </button>
        `;
        
        container.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));
        
        // Auto remove
        setTimeout(() => {
            this.removeToast(toast);
        }, duration);
        
        return toast;
    },
    
    // Remove toast
    removeToast(toast) {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    },
    
    // Modal system
    initializeModals() {
        // Add modal styles if not present
        if (!document.querySelector('#modal-styles')) {
            const styles = document.createElement('style');
            styles.id = 'modal-styles';
            styles.textContent = `
                .toast { pointer-events: auto; }
                .modal-backdrop { 
                    position: fixed; 
                    top: 0; 
                    left: 0; 
                    right: 0; 
                    bottom: 0; 
                    background: rgba(0,0,0,0.5); 
                    z-index: 1000; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center; 
                    opacity: 0; 
                    visibility: hidden; 
                    transition: all 0.3s ease; 
                }
                .modal-backdrop.active { opacity: 1; visibility: visible; }
                .modal-content { 
                    background: var(--color-white); 
                    border-radius: var(--border-radius-lg); 
                    max-width: 90vw; 
                    max-height: 90vh; 
                    overflow-y: auto; 
                    transform: scale(0.9); 
                    transition: transform 0.3s ease; 
                }
                .modal-backdrop.active .modal-content { transform: scale(1); }
            `;
            document.head.appendChild(styles);
        }
    },
    
    // Create and show modal
    createModal(title, content, options = {}) {
        const modal = document.createElement('div');
        modal.className = 'modal-backdrop';
        
        const width = options.width || '500px';
        
        modal.innerHTML = `
            <div class="modal-content" style="width: ${width};">
                <div class="modal-header" style="padding: 1.5rem; border-bottom: 1px solid var(--color-border); display: flex; align-items: center; justify-content: space-between;">
                    <h3 style="margin: 0; font-size: var(--font-size-lg); font-weight: 600;">${title}</h3>
                    <button class="modal-close" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--color-tertiary);">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="modal-body" style="padding: 1.5rem;">
                    ${content}
                </div>
                ${options.footer ? `<div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid var(--color-border); display: flex; gap: 1rem; justify-content: flex-end;">${options.footer}</div>` : ''}
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listeners
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => this.closeModal(modal));
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });
        
        // Show modal
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
        
        return modal;
    },
    
    // Close modal
    closeModal(modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    },
    
    // Table utilities
    initializeTables() {
        // Add sorting functionality to tables
        document.querySelectorAll('.table th').forEach(header => {
            if (!header.querySelector('.sort-icon')) {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => this.sortTable(header));
            }
        });
    },
    
    // Sort table by column
    sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        
        // Determine sort direction
        const isAscending = !header.classList.contains('sort-asc');
        
        // Remove existing sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add sort class
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            // Try to parse as numbers
            const aNum = parseFloat(aValue.replace(/[^0-9.-]/g, ''));
            const bNum = parseFloat(bValue.replace(/[^0-9.-]/g, ''));
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        });
        
        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    },
    
    // Form utilities
    initializeForms() {
        // Add form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        });
        
        // Add input formatting
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', this.formatNumberInput.bind(this));
        });
    },
    
    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        
        if (this.validateForm(form)) {
            this.showToast('Form submitted successfully!', 'success');
            // TODO: Implement actual form submission
        }
    },
    
    // Validate form
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });
        
        return isValid;
    },
    
    // Show field error
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        const error = document.createElement('div');
        error.className = 'field-error';
        error.style.cssText = 'color: var(--color-error); font-size: var(--font-size-xs); margin-top: 4px;';
        error.textContent = message;
        
        field.style.borderColor = 'var(--color-error)';
        field.parentNode.appendChild(error);
    },
    
    // Clear field error
    clearFieldError(field) {
        const error = field.parentNode.querySelector('.field-error');
        if (error) {
            error.remove();
        }
        field.style.borderColor = '';
    },
    
    // Format number input
    formatNumberInput(e) {
        const input = e.target;
        let value = input.value.replace(/[^0-9.]/g, '');
        
        // Ensure only one decimal point
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        input.value = value;
    },
    
    // Create confirmation dialog
    confirm(message, onConfirm, onCancel) {
        const modal = this.createModal(
            'Confirm Action',
            `<p style="margin: 0; color: var(--color-secondary);">${message}</p>`,
            {
                footer: `
                    <button class="btn btn-secondary cancel-btn">Cancel</button>
                    <button class="btn btn-primary confirm-btn">Confirm</button>
                `
            }
        );
        
        const confirmBtn = modal.querySelector('.confirm-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');
        
        confirmBtn.addEventListener('click', () => {
            this.closeModal(modal);
            if (onConfirm) onConfirm();
        });
        
        cancelBtn.addEventListener('click', () => {
            this.closeModal(modal);
            if (onCancel) onCancel();
        });
        
        return modal;
    },
    
    // Create loading overlay
    showLoading(container, message = 'Loading...') {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 1rem;
            z-index: 10;
        `;
        
        overlay.innerHTML = `
            <div class="loading-spinner"></div>
            <span style="color: var(--color-tertiary); font-size: var(--font-size-sm);">${message}</span>
        `;
        
        container.style.position = 'relative';
        container.appendChild(overlay);
        
        return overlay;
    },
    
    // Hide loading overlay
    hideLoading(container) {
        const overlay = container.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    },
    
    // Format currency
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // Format date
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
    },
    
    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Components.init();
});

// Export for use in other modules
window.Components = Components;
