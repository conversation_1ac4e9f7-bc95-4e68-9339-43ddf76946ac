/* CSS Custom Properties for consistent theming */
:root {
    --color-primary: #000000;
    --color-secondary: #333333;
    --color-tertiary: #666666;
    --color-quaternary: #999999;
    --color-light: #f5f5f5;
    --color-white: #ffffff;
    --color-border: #e0e0e0;
    --color-hover: #f0f0f0;
    --color-active: #e8e8e8;
    --color-success: #4caf50;
    --color-warning: #ff9800;
    --color-error: #f44336;

    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    --transition: all 0.2s ease-in-out;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-primary);
    background-color: var(--color-white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--color-secondary);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-tertiary { color: var(--color-tertiary); }
.text-quaternary { color: var(--color-quaternary); }

.bg-white { background-color: var(--color-white); }
.bg-light { background-color: var(--color-light); }
.bg-primary { background-color: var(--color-primary); }

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }

/* Border utilities */
.border { border: 1px solid var(--color-border); }
.border-t { border-top: 1px solid var(--color-border); }
.border-b { border-bottom: 1px solid var(--color-border); }
.border-l { border-left: 1px solid var(--color-border); }
.border-r { border-right: 1px solid var(--color-border); }

.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Transition utilities */
.transition { transition: var(--transition); }

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    gap: var(--spacing-xs);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.btn-primary:hover {
    background-color: var(--color-secondary);
}

.btn-secondary {
    background-color: var(--color-white);
    color: var(--color-primary);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: var(--color-hover);
}

/* Icon button */
.icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius);
    background-color: transparent;
    color: var(--color-tertiary);
    cursor: pointer;
    transition: var(--transition);
}

.icon-button:hover {
    background-color: var(--color-hover);
    color: var(--color-primary);
}

/* Form elements */
input, textarea, select {
    font-family: inherit;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* UNIFIED DESIGN SYSTEM - CONSISTENT ACROSS ALL PAGES */

/* Modern Page Layout System */
.modern-page-container {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: var(--spacing-lg);
    min-height: calc(100vh - 200px);
}

.modern-main-content {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: var(--spacing-md);
}

.modern-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Modern Header Component */
.modern-header {
    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
    color: white;
    padding: var(--spacing-lg);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.modern-header-content {
    position: relative;
    z-index: 1;
}

.modern-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
}

.modern-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin: 0;
}

/* Enhanced Metrics System */
.metrics-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.metric-modern {
    background: var(--color-white);
    border-radius: 16px;
    padding: var(--spacing-lg);
    border: 1px solid var(--color-border);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.metric-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color, #000);
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 0.3s ease;
}

.metric-modern:hover::before {
    transform: scaleY(1);
}

.metric-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
}

.metric-modern.primary { --accent-color: #2196F3; }
.metric-modern.warning { --accent-color: #FF9800; }
.metric-modern.success { --accent-color: #4CAF50; }
.metric-modern.error { --accent-color: #F44336; }

.metric-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.metric-icon-modern {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--accent-color, #000);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb, 0,0,0), 0.3);
}

.metric-trend-modern {
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    background: var(--trend-bg, #f5f5f5);
    color: var(--trend-color, #666);
}

.metric-trend-modern.positive {
    --trend-bg: rgba(76, 175, 80, 0.1);
    --trend-color: #4CAF50;
}

.metric-trend-modern.negative {
    --trend-bg: rgba(244, 67, 54, 0.1);
    --trend-color: #F44336;
}

.metric-value-modern {
    font-size: 2rem;
    font-weight: 800;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.metric-label-modern {
    font-size: var(--font-size-base);
    color: var(--color-secondary);
    font-weight: 600;
}

/* Enhanced Action Bar */
.action-bar-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
    background: var(--color-white);
    padding: var(--spacing-lg);
    border-radius: 16px;
    border: 1px solid var(--color-border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.actions-left-modern {
    display: flex;
    gap: var(--spacing-md);
}

.actions-right-modern {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* Enhanced Button System */
.btn-modern {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    border: none;
    cursor: pointer;
    text-decoration: none;
    gap: var(--spacing-xs);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.btn-modern.primary {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.btn-modern.secondary {
    background-color: var(--color-white);
    color: var(--color-primary);
    border: 1px solid var(--color-border);
}

/* Modern Table System */
.table-modern {
    background: var(--color-white);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid var(--color-border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-modern table {
    width: 100%;
    border-collapse: collapse;
}

.table-modern th,
.table-modern td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
}

.table-modern th {
    background-color: var(--color-light);
    font-weight: 600;
    color: var(--color-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern tbody tr:hover {
    background-color: var(--color-hover);
}

.table-modern tbody tr:last-child td {
    border-bottom: none;
}

/* Modern Card System */
.card-modern {
    background: var(--color-white);
    border-radius: 16px;
    border: 1px solid var(--color-border);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-modern:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header-modern {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-light);
    font-weight: 600;
    font-size: var(--font-size-base);
    color: var(--color-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-content-modern {
    padding: var(--spacing-lg);
}

/* Status Badge System */
.status-modern {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-modern.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
}

.status-modern.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--color-warning);
}

.status-modern.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--color-error);
}

.status-modern.neutral {
    background-color: var(--color-light);
    color: var(--color-tertiary);
}

/* Action Button System */
.action-buttons-modern {
    display: flex;
    gap: var(--spacing-xs);
}

.action-btn-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius);
    background-color: transparent;
    color: var(--color-tertiary);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn-modern:hover {
    background-color: var(--color-hover);
    color: var(--color-primary);
}

/* Filter System */
.filter-modern {
    min-width: 150px;
    border-radius: 12px;
    border: 2px solid var(--color-border);
    transition: all 0.3s ease;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.filter-modern:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
}

/* Animation System */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-slide-up {
    animation: slideInUp 0.4s ease-out;
}

.animate-fade-scale {
    animation: fadeInScale 0.4s ease-out;
}

/* Responsive design */
@media (max-width: 1024px) {
    .modern-page-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .modern-sidebar {
        order: -1;
    }

    .metrics-modern {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-3xl: 1.5rem;
        --font-size-2xl: 1.25rem;
        --font-size-xl: 1.125rem;
    }

    .grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .grid-cols-2 { grid-template-columns: repeat(1, minmax(0, 1fr)); }

    .metrics-modern {
        grid-template-columns: 1fr;
    }

    .modern-title {
        font-size: 1.5rem;
    }

    .action-bar-modern {
        flex-direction: column;
        align-items: stretch;
    }

    .actions-left-modern,
    .actions-right-modern {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .metric-value-modern {
        font-size: 1.5rem;
    }
}
