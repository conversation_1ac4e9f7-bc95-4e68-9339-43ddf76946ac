/* Reusable component styles */

/* Table styles */
.table-container {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--color-border);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
}

.table th {
    background-color: var(--color-light);
    font-weight: 600;
    color: var(--color-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    color: var(--color-secondary);
    font-size: var(--font-size-sm);
}

.table tbody tr:hover {
    background-color: var(--color-hover);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Form styles */
.form-container {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
}

/* Card styles */
.card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-content {
    color: var(--color-secondary);
    line-height: 1.6;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
}

.status-badge.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--color-warning);
}

.status-badge.error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--color-error);
}

.status-badge.neutral {
    background-color: var(--color-light);
    color: var(--color-tertiary);
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius);
    background-color: transparent;
    color: var(--color-tertiary);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background-color: var(--color-hover);
    color: var(--color-primary);
}

.action-btn.edit:hover {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.action-btn.delete:hover {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--color-error);
}

.action-btn.view:hover {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background-color: var(--color-white);
    border-top: 1px solid var(--color-border);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--color-tertiary);
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background-color: var(--color-white);
    color: var(--color-tertiary);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.pagination-btn:hover {
    background-color: var(--color-hover);
    color: var(--color-primary);
}

.pagination-btn.active {
    background-color: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-border);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--color-tertiary);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--color-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .pagination {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
}
