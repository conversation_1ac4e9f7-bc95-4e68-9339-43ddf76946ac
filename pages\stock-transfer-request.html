<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Transfer Request - Inventory Management System</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>
            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>
            <div class="header-right">
                <button class="icon-button"><span class="material-icons">notifications</span></button>
                <button class="icon-button"><span class="material-icons">language</span></button>
                <button class="icon-button"><span class="material-icons">light_mode</span></button>
                <button class="icon-button"><span class="material-icons">settings</span></button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="dashboard.html" class="nav-item" data-page="dashboard">
                    <span class="material-icons">dashboard</span><span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span><span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span><span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span><span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span><span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span><span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item active" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span><span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span><span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h2>Stock Transfer Request</h2>
                <p>Request and manage stock transfers</p>
            </div>

            <div class="page-actions" style="display: flex; justify-content: space-between; margin-bottom: 2rem;">
                <button class="btn btn-primary">
                    <span class="material-icons">add</span>
                    New Transfer Request
                </button>
                <button class="btn btn-secondary">
                    <span class="material-icons">file_download</span>
                    Export
                </button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Transfer ID</th>
                            <th>From Location</th>
                            <th>To Location</th>
                            <th>Request Date</th>
                            <th>Items</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ST-2024-001</strong></td>
                            <td>Warehouse A</td>
                            <td>Warehouse B</td>
                            <td>2024-01-15</td>
                            <td>12 items</td>
                            <td><span class="status-badge success">Completed</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn view"><span class="material-icons">visibility</span></button>
                                    <button class="action-btn"><span class="material-icons">file_download</span></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>ST-2024-002</strong></td>
                            <td>Warehouse B</td>
                            <td>Store Front</td>
                            <td>2024-01-14</td>
                            <td>5 items</td>
                            <td><span class="status-badge warning">Pending</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn view"><span class="material-icons">visibility</span></button>
                                    <button class="action-btn edit"><span class="material-icons">edit</span></button>
                                    <button class="action-btn"><span class="material-icons">file_download</span></button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Navigation) {
                window.Navigation.currentPage = 'stock-transfer-request';
            }
        });
    </script>
</body>
</html>
