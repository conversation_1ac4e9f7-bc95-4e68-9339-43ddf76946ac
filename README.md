# Inventory Management System

A modern, professional web application for complete inventory management business solutions.

## Features

- **Dashboard**: Real-time metrics and insights
- **Purchase Orders**: Create and manage purchase orders
- **Purchase Invoices**: Handle invoice processing and payments
- **Purchase GRN**: Goods Receipt Notes management
- **GDR Settlement**: Goods Delivery Receipt settlements
- **PO Cancellation**: Purchase order cancellation management
- **Stock Transfer Request**: Inter-location stock transfers
- **Stock Receipt GRN**: Stock receipt processing

## Technology Stack

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Fonts**: Google Fonts (Inter)
- **Icons**: Google Material Icons
- **Design**: Modern, minimalist UI with black/white/gray color scheme
- **Responsive**: Mobile-first design approach

## Project Structure

```
StockReceived/
├── index.html                 # Main entry point (Dashboard)
├── layouts/
│   └── layout.html           # Master layout template
├── pages/                    # Individual page files
│   ├── dashboard.html
│   ├── purchase-order.html
│   ├── purchase-invoice.html
│   ├── purchase-grn.html
│   ├── gdr-settlement.html
│   ├── po-cancellation.html
│   ├── stock-transfer-request.html
│   └── stock-receipt-grn.html
├── styles/                   # CSS files
│   ├── main.css             # Global styles and utilities
│   ├── layout.css           # Layout-specific styles
│   └── components.css       # Reusable component styles
├── scripts/                  # JavaScript files
│   ├── main.js              # Core application logic
│   ├── navigation.js        # Navigation and routing
│   └── components.js        # Reusable components and utilities
└── README.md                # Project documentation
```

## Design Features

### Color Scheme
- Primary: Black (#000000)
- Secondary: Dark Gray (#333333)
- Tertiary: Medium Gray (#666666)
- Light: Light Gray (#f5f5f5)
- White: Pure White (#ffffff)
- Accent colors for status indicators

### Typography
- Font Family: Inter (Google Fonts)
- Responsive font sizes
- Consistent spacing and hierarchy

### Layout
- Sticky header with search functionality
- Horizontal navigation with active states
- Responsive grid system
- Card-based design for metrics and content
- Professional table layouts with sorting and pagination

### Components
- Interactive dashboard cards with hover effects
- Modal dialogs for forms and confirmations
- Toast notifications for user feedback
- Status badges with color coding
- Action buttons with tooltips
- Responsive tables with filtering

## Key Features

### Dashboard
- Real-time metrics display
- Recent activity feed
- Alert notifications
- Quick action cards
- Animated number counters

### Purchase Orders
- Create new purchase orders
- Filter and search functionality
- Status tracking (Pending, Approved, Completed)
- Export capabilities
- Detailed action buttons (View, Edit, Download, Delete)

### Purchase Invoices
- Invoice management with payment tracking
- Bulk payment processing
- Overdue invoice highlighting
- Status management (Pending, Paid, Overdue)
- Payment reminder functionality

### Navigation
- Single Page Application (SPA) behavior
- URL-based routing
- Active state management
- Mobile-responsive navigation
- Keyboard shortcuts (Ctrl+K for search)

### User Experience
- Smooth transitions and animations
- Loading states and feedback
- Confirmation dialogs for destructive actions
- Form validation with error messages
- Responsive design for all screen sizes

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for tablets and mobile devices

## Getting Started

1. Clone or download the project files
2. Open `index.html` in a web browser
3. Navigate through the application using the top navigation menu
4. All functionality is client-side and requires no server setup

## Customization

### Colors
Modify CSS custom properties in `styles/main.css`:
```css
:root {
    --color-primary: #000000;
    --color-secondary: #333333;
    /* ... other color variables */
}
```

### Layout
Adjust layout settings in `styles/layout.css` for:
- Header height and spacing
- Navigation styling
- Main content padding
- Responsive breakpoints

### Components
Extend or modify components in `styles/components.css`:
- Table styles
- Form elements
- Modal dialogs
- Status badges

## JavaScript Architecture

### Main.js
- Application initialization
- Global event handlers
- Search functionality
- Theme management
- Toast notifications

### Navigation.js
- Page routing and navigation
- Content loading
- URL management
- Page-specific initialization

### Components.js
- Reusable UI components
- Modal system
- Form validation
- Table utilities
- Helper functions

## Future Enhancements

- Backend API integration
- User authentication
- Real-time data updates
- Advanced reporting
- Print functionality
- Dark mode theme
- Multi-language support
- Advanced filtering and search
- Data export in multiple formats
- Mobile app version

## License

This project is created for demonstration purposes. Feel free to use and modify as needed.

## Support

For questions or support, please refer to the code comments and documentation within the files.
