// Navigation functionality for the Inventory Management System

const Navigation = {
    currentPage: 'dashboard',

    // Initialize navigation
    init() {
        this.setupNavigationEvents();
        this.updateActiveNavItem();
    },

    // Set up navigation event listeners
    setupNavigationEvents() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            // Remove preventDefault to allow natural navigation
            item.addEventListener('click', this.handleNavigation.bind(this));
        });
    },

    // Handle navigation clicks (simplified for direct file navigation)
    handleNavigation(e) {
        // Allow natural navigation - no preventDefault
        const navItem = e.currentTarget;
        const page = navItem.getAttribute('data-page');

        // Update current page for any page-specific logic
        if (page) {
            this.currentPage = page;
        }
    },

    // Update active navigation item based on current page
    updateActiveNavItem() {
        // Get current page from URL path
        const currentPath = window.location.pathname;
        const currentPage = this.getCurrentPageFromPath(currentPath);

        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current nav item
        const activeItem = document.querySelector(`[data-page="${currentPage}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // Update current page
        this.currentPage = currentPage;
    },

    // Get current page from URL path
    getCurrentPageFromPath(path) {
        if (path.includes('purchase-order')) return 'purchase-order';
        if (path.includes('purchase-invoice')) return 'purchase-invoice';
        if (path.includes('purchase-grn')) return 'purchase-grn';
        if (path.includes('gdr-settlement')) return 'gdr-settlement';
        if (path.includes('po-cancellation')) return 'po-cancellation';
        if (path.includes('stock-transfer-request')) return 'stock-transfer-request';
        if (path.includes('stock-receipt-grn')) return 'stock-receipt-grn';
        return 'dashboard';
    },

    // Initialize page-specific features (simplified for direct file navigation)
    initializePageFeatures() {
        // Add page-specific event listeners and functionality based on current page
        switch (this.currentPage) {
            case 'dashboard':
                this.initializeDashboard();
                break;
            case 'purchase-order':
                this.initializePurchaseOrders();
                break;
            // Add other page initializations as needed
        }
    },

    // Initialize dashboard features
    initializeDashboard() {
        // Animate dashboard metrics
        if (window.App && window.App.loadDashboardData) {
            window.App.loadDashboardData();
        }
    },

    // Initialize purchase orders features
    initializePurchaseOrders() {
        // Add event listeners for purchase order actions
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', this.handleTableAction.bind(this));
        });
    },

    // Handle table actions
    handleTableAction(e) {
        const button = e.currentTarget;
        const action = button.classList.contains('view') ? 'view' :
                      button.classList.contains('edit') ? 'edit' : 'delete';

        console.log(`${action} action clicked`);
        // TODO: Implement specific actions
    }
};

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Navigation.init();
    // Initialize page-specific features
    Navigation.initializePageFeatures();
});

// Export for use in other modules
window.Navigation = Navigation;
